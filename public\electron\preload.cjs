// 预加载脚本：暴露安全的API给渲染进程
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  getBooks: () => ipcRenderer.invoke('library:getBooks'),
  addBook: (book) => ipcRenderer.invoke('library:addBook', book),
  getFavorites: () => ipcRenderer.invoke('library:getFavorites'),
  addFavorite: (bookId) => ipcRenderer.invoke('library:addFavorite', bookId),
  removeFavorite: (bookId) => ipcRenderer.invoke('library:removeFavorite', bookId)
});
