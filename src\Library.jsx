import { useEffect, useState } from 'react';

export function Library({ onOpen }) {
  const [books, setBooks] = useState([]);

  useEffect(() => {
    window.electronAPI.getBooks().then(setBooks);
  }, []);

  return (
    <div className="book-list">
      <h2>书库</h2>
      {books.length === 0 ? (
        <div className="empty-state">
          <p>书库为空，请添加EPUB文件</p>
        </div>
      ) : (
        <ul>
          {books.map((book, idx) => (
            <li key={book.id || idx}>
              <span>{book.title || book.fileName}</span>
              <button onClick={() => onOpen(book)}>阅读</button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export function Favorites({ onOpen }) {
  const [favorites, setFavorites] = useState([]);
  const [books, setBooks] = useState([]);

  useEffect(() => {
    window.electronAPI.getFavorites().then(setFavorites);
    window.electronAPI.getBooks().then(setBooks);
  }, []);

  const favBooks = books.filter(b => favorites.includes(b.id));

  return (
    <div className="book-list">
      <h2>收藏</h2>
      {favBooks.length === 0 ? (
        <div className="empty-state">
          <p>暂无收藏的书籍</p>
        </div>
      ) : (
        <ul>
          {favBooks.map((book, idx) => (
            <li key={book.id || idx}>
              <span>{book.title || book.fileName}</span>
              <button onClick={() => onOpen(book)}>阅读</button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
