
import { useRef, useState } from 'react';
import { Library, Favorites } from './Library';
import './App.css';


function App() {
  const viewerRef = useRef(null);
  const [currentBook, setCurrentBook] = useState(null);
  const [tab, setTab] = useState('library');

  // 选择文件并加载 epub
  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (file && viewerRef.current) {
      const url = URL.createObjectURL(file);
      const ePub = (await import('epubjs')).default;
      const book = ePub(url);
      book.renderTo(viewerRef.current, { width: '100%', height: 600 });
      // 保存到本地书库
      const meta = {
        id: Date.now().toString(),
        fileName: file.name,
        filePath: url,
        title: file.name.replace(/\.epub$/, ''),
        addedAt: Date.now()
      };
      await window.electronAPI.addBook(meta);
    }
  };

  // 打开书籍
  const handleOpenBook = async (book) => {
    setCurrentBook(book);
    if (viewerRef.current) {
      const ePub = (await import('epubjs')).default;
      const bookObj = ePub(book.filePath);
      bookObj.renderTo(viewerRef.current, { width: '100%', height: 600 });
    }
  };

  // 收藏当前书籍
  const handleFavorite = async () => {
    if (currentBook) {
      await window.electronAPI.addFavorite(currentBook.id);
      alert('已收藏！');
    }
  };

  return (
    <div className="App">
      <h1>EPUB 阅读器</h1>
      <div className="nav-bar">
        <button
          className={tab === 'library' ? 'active' : ''}
          onClick={() => setTab('library')}
        >
          书库
        </button>
        <button
          className={tab === 'favorites' ? 'active' : ''}
          onClick={() => setTab('favorites')}
        >
          收藏
        </button>
        <input type="file" accept=".epub" onChange={handleFileChange} />
      </div>
      {tab === 'library' && <Library onOpen={handleOpenBook} />}
      {tab === 'favorites' && <Favorites onOpen={handleOpenBook} />}
      <div ref={viewerRef} className="reader-container" />
      {currentBook && (
        <div className="current-book-info">
          <span>当前阅读：{currentBook.title || currentBook.fileName}</span>
          <button onClick={handleFavorite}>收藏</button>
        </div>
      )}
    </div>
  );
}

export default App;
