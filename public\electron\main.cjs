const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
// 使用动态导入来加载ESM模块
let Store;
(async () => {
  const module = await import('electron-store');
  Store = module.default;
})();

// 初始化本地存储
const store = new Store({
  name: 'library',
  defaults: {
    books: [], // 书库
    favorites: [] // 收藏
  }
});

function createWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  // 在开发模式下加载Vite开发服务器，在生产模式下加载构建后的文件
  const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

  if (isDev) {
    win.loadURL('http://localhost:5173');
    // 在开发模式下打开开发者工具
    win.webContents.openDevTools();
  } else {
    win.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // 主进程监听渲染进程的书库/收藏相关请求
  ipcMain.handle('library:getBooks', () => {
    return store.get('books');
  });
  ipcMain.handle('library:addBook', (_, book) => {
    const books = store.get('books');
    books.push(book);
    store.set('books', books);
    return books;
  });
  ipcMain.handle('library:getFavorites', () => {
    return store.get('favorites');
  });
  ipcMain.handle('library:addFavorite', (_, bookId) => {
    const favorites = store.get('favorites');
    if (!favorites.includes(bookId)) {
      favorites.push(bookId);
      store.set('favorites', favorites);
    }
    return favorites;
  });
  ipcMain.handle('library:removeFavorite', (_, bookId) => {
    let favorites = store.get('favorites');
    favorites = favorites.filter(id => id !== bookId);
    store.set('favorites', favorites);
    return favorites;
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) createWindow();
});
