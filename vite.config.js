import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: './', // 确保在Electron中能正确加载资源
  build: {
    outDir: 'dist', // 构建输出目录
    emptyOutDir: true,
    rollupOptions: {
      output: {
        // 确保资源文件路径正确
        assetFileNames: 'assets/[name].[hash].[ext]',
        chunkFileNames: 'assets/[name].[hash].js',
        entryFileNames: 'assets/[name].[hash].js'
      }
    }
  },
  server: {
    port: 5173, // 开发服务器端口
    strictPort: true, // 如果端口被占用则退出
  }
})
