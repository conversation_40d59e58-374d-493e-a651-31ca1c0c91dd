{"name": "readelc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "cross-env NODE_ENV=development electron ./public/electron/main.cjs", "electron:prod": "cross-env NODE_ENV=production electron ./public/electron/main.cjs", "dev:electron": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && npm run electron\" --kill-others", "build:electron": "npm run build && npm run electron:prod", "dist": "npm run build && electron-builder"}, "dependencies": {"concurrently": "^8.2.2", "electron-store": "^10.1.0", "epubjs": "^0.3.93", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "cross-env": "^7.0.3", "electron": "^37.2.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^5.4.0", "wait-on": "^8.0.3"}}