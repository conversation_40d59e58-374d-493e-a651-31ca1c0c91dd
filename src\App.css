.App {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.App h1 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.nav-bar button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.nav-bar button:hover {
  background-color: #0056b3;
}

.nav-bar button.active {
  background-color: #28a745;
}

.nav-bar input[type="file"] {
  margin-left: auto;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 书库和收藏列表样式 */
.book-list {
  margin-bottom: 20px;
}

.book-list h2 {
  color: #555;
  margin-bottom: 15px;
}

.book-list ul {
  list-style: none;
  padding: 0;
}

.book-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.book-list li:hover {
  background-color: #e9ecef;
}

.book-list li span {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.book-list li button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #17a2b8;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.book-list li button:hover {
  background-color: #138496;
}

/* 阅读器样式 */
.reader-container {
  border: 2px solid #dee2e6;
  border-radius: 8px;
  margin-top: 20px;
  min-height: 600px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 当前阅读信息样式 */
.current-book-info {
  margin-top: 15px;
  padding: 10px;
  background-color: #e7f3ff;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-book-info span {
  font-weight: 500;
  color: #0056b3;
}

.current-book-info button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #ffc107;
  color: #212529;
  cursor: pointer;
  transition: background-color 0.2s;
}

.current-book-info button:hover {
  background-color: #e0a800;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}
